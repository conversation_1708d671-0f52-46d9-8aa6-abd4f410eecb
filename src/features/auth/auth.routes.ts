import { OpenAPIRoute } from "chanfana";
import { type Context } from "hono";
import { z } from "zod";
import { getFirebaseToken } from "@hono/firebase-auth";
import type { HonoEnv } from "../../types";
import { authMiddleware } from "../../middleware/auth.middleware";

/**
 * 获取当前用户信息的端点
 */
export class GetCurrentUserEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Get current authenticated user information",
    description:
      "Returns the current authenticated user's information from Firebase token",
    tags: ["Authentication"],
    security: [
      {
        BearerAuth: [],
      },
    ],
    responses: {
      200: {
        description: "Current user information",
        content: {
          "application/json": {
            schema: z.object({
              uid: z.string().describe("User unique identifier"),
              email: z
                .string()
                .email()
                .optional()
                .describe("User email address"),
              emailVerified: z
                .boolean()
                .optional()
                .describe("Whether email is verified"),
              phoneNumber: z.string().optional().describe("User phone number"),
              picture: z
                .string()
                .url()
                .optional()
                .describe("User profile picture URL"),
              signInProvider: z
                .string()
                .describe("Authentication provider used"),
              authTime: z.number().describe("Authentication timestamp"),
              customClaims: z
                .record(z.any())
                .describe("Custom claims from Firebase"),
            }),
          },
        },
      },
      401: {
        description: "Unauthorized - No valid Firebase token",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    const firebaseToken = getFirebaseToken(c);

    if (!firebaseToken) {
      return c.json({ error: "Unauthorized" }, 401);
    }

    // 提取自定义声明（排除标准 JWT 声明）
    const standardClaims = [
      "uid",
      "email",
      "email_verified",
      "phone_number",
      "picture",
      "auth_time",
      "iat",
      "exp",
      "aud",
      "iss",
      "sub",
      "firebase",
    ];
    const customClaims = Object.keys(firebaseToken)
      .filter((key) => !standardClaims.includes(key))
      .reduce((acc, key) => {
        acc[key] = firebaseToken[key];
        return acc;
      }, {} as Record<string, any>);

    return c.json({
      uid: firebaseToken.uid,
      email: firebaseToken.email,
      emailVerified: firebaseToken.email_verified,
      phoneNumber: firebaseToken.phone_number,
      picture: firebaseToken.picture,
      signInProvider: firebaseToken.firebase.sign_in_provider,
      authTime: firebaseToken.auth_time,
      customClaims,
    });
  }
}

/**
 * 检查用户权限的端点
 */
export class CheckPermissionEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Check user permissions",
    description: "Check if the current user has specific permissions",
    tags: ["Authentication"],
    request: {
      query: z.object({
        permission: z.string().describe("Permission to check"),
      }),
    },
    responses: {
      200: {
        description: "Permission check result",
        content: {
          "application/json": {
            schema: z.object({
              hasPermission: z
                .boolean()
                .describe("Whether user has the permission"),
              uid: z.string().describe("User ID"),
              permissions: z.array(z.string()).describe("All user permissions"),
            }),
          },
        },
      },
      401: {
        description: "Unauthorized",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    const firebaseToken = getFirebaseToken(c);

    if (!firebaseToken) {
      return c.json({ error: "Unauthorized" }, 401);
    }

    const { permission } = c.req.query();

    // 从自定义声明中获取用户权限
    const userPermissions = firebaseToken.permissions || [];
    const userRole = firebaseToken.role || "user";

    // 基于角色的权限检查
    const rolePermissions: Record<string, string[]> = {
      admin: ["read", "write", "delete", "manage_users"],
      editor: ["read", "write"],
      user: ["read"],
    };

    const allPermissions = [
      ...userPermissions,
      ...(rolePermissions[userRole] || []),
    ];

    const hasPermission = allPermissions.includes(permission);

    return c.json({
      hasPermission,
      uid: firebaseToken.uid,
      permissions: allPermissions,
    });
  }
}

/**
 * 用户资源访问控制示例端点
 */
export class GetUserResourceEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Get user-specific resource",
    description: "Get a resource that belongs to the authenticated user",
    tags: ["Authentication"],
    request: {
      params: z.object({
        resourceId: z.string().describe("Resource ID to access"),
      }),
    },
    responses: {
      200: {
        description: "Resource data",
        content: {
          "application/json": {
            schema: z.object({
              resourceId: z.string(),
              ownerId: z.string(),
              data: z.any(),
              accessTime: z.string(),
            }),
          },
        },
      },
      401: {
        description: "Unauthorized",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
      403: {
        description: "Forbidden - Resource doesn't belong to user",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    const firebaseToken = getFirebaseToken(c);

    if (!firebaseToken) {
      return c.json({ error: "Unauthorized" }, 401);
    }

    const resourceId = c.req.param("resourceId");

    // 模拟从数据库获取资源
    // 在实际应用中，这里会查询数据库
    const mockResource = {
      id: resourceId,
      ownerId: firebaseToken.uid, // 假设资源属于当前用户
      data: { message: "This is user-specific data" },
    };

    // 检查资源所有权
    if (mockResource.ownerId !== firebaseToken.uid) {
      return c.json(
        {
          error: "Forbidden: You can only access your own resources",
        },
        403
      );
    }

    return c.json({
      resourceId: mockResource.id,
      ownerId: mockResource.ownerId,
      data: mockResource.data,
      accessTime: new Date().toISOString(),
    });
  }
}

/**
 * 管理员专用端点
 */
export class AdminOnlyEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Admin only endpoint",
    description: "Endpoint that requires admin role",
    tags: ["Authentication"],
    responses: {
      200: {
        description: "Admin data",
        content: {
          "application/json": {
            schema: z.object({
              message: z.string(),
              adminUid: z.string(),
              systemInfo: z.any(),
            }),
          },
        },
      },
      401: {
        description: "Unauthorized",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
            }),
          },
        },
      },
      403: {
        description: "Forbidden - Admin role required",
        content: {
          "application/json": {
            schema: z.object({
              error: z.string(),
              required: z.string(),
              current: z.string(),
            }),
          },
        },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    const firebaseToken = getFirebaseToken(c);

    if (!firebaseToken) {
      return c.json({ error: "Unauthorized" }, 401);
    }

    const userRole = firebaseToken.role || "user";

    if (userRole !== "admin") {
      return c.json(
        {
          error: "Forbidden: Admin role required",
          required: "admin",
          current: userRole,
        },
        403
      );
    }

    return c.json({
      message: "Welcome admin!",
      adminUid: firebaseToken.uid,
      systemInfo: {
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || "development",
      },
    });
  }
}

/**
 * 注册认证相关路由
 */
export function registerAuthRoutes(
  openapi: any,
  routePrefix: string = "/auth"
) {
  const base = routePrefix.startsWith("/") ? routePrefix : `/${routePrefix}`;

  // 需要认证的路由
  openapi.get(`${base}/me`, authMiddleware, GetCurrentUserEndpoint);
  openapi.get(
    `${base}/check-permission`,
    authMiddleware,
    CheckPermissionEndpoint
  );
  openapi.get(
    `${base}/resource/:resourceId`,
    authMiddleware,
    GetUserResourceEndpoint
  );
  openapi.get(`${base}/admin`, authMiddleware, AdminOnlyEndpoint);
}
