import { OpenAPIRoute } from "chanfana";
import { type Context } from "hono";
import { z } from "zod";
import { inject } from "tsyringe";
import { getFirebaseToken } from "@hono/firebase-auth";

import type { HonoEnv } from "../../../types";
import { IUserService, type UserService } from "../user.interface";
import { 
  UserResponseSchema, 
  GenericErrorSchema,
  ZodValidationErrorSchema 
} from "../user.schema";

// Request schema for check-or-create user
export const checkOrCreateUserBodySchema = z.object({
  email: z.string().email({ message: "Invalid email address" }),
  displayName: z.string().optional(),
  photoURL: z.string().url({ message: "Invalid URL format" }).optional(),
});

// Response schema with is_new_user flag
export const CheckOrCreateUserResponseSchema = z.object({
  user: UserResponseSchema,
  is_new_user: z.boolean(),
  message: z.string().optional(),
});

export class CheckOrCreateUserEndpoint extends OpenAPIRoute {
  schema = {
    summary: "Check if user exists or create new user",
    description: "Checks if user exists in database. If not, creates new user and handles registration logic. Returns user data with is_new_user flag.",
    tags: ["Users"],
    security: [
      {
        BearerAuth: [],
      },
    ],
    request: {
      body: {
        content: {
          "application/json": {
            schema: checkOrCreateUserBodySchema,
          },
        },
        required: true,
      },
    },
    responses: {
      "200": {
        description: "User exists - returned existing user data",
        content: {
          "application/json": {
            schema: CheckOrCreateUserResponseSchema,
          },
        },
      },
      "201": {
        description: "New user created successfully",
        content: {
          "application/json": {
            schema: CheckOrCreateUserResponseSchema,
          },
        },
      },
      "400": {
        description: "Validation failed or invalid input",
        content: { "application/json": { schema: ZodValidationErrorSchema } },
      },
      "401": {
        description: "Unauthorized - Invalid Firebase token",
        content: { "application/json": { schema: GenericErrorSchema } },
      },
      "500": {
        description: "Internal server error",
        content: { "application/json": { schema: GenericErrorSchema } },
      },
    },
  };

  async handle(c: Context<HonoEnv>) {
    try {
      // 1. Verify Firebase token
      const firebaseToken = getFirebaseToken(c);
      if (!firebaseToken) {
        return c.json({ error: "Unauthorized" }, 401);
      }

      const uid = firebaseToken.uid;
      const { body } = await this.getValidatedData<typeof this.schema>();
      
      const container = c.get("container");
      const userService = container.resolve<UserService>(IUserService);

      // 2. Check if user already exists
      const existingUser = await userService.getUserById(uid);
      
      if (existingUser) {
        // User exists - return existing user data
        return c.json({
          user: existingUser,
          is_new_user: false,
          message: "User already exists"
        }, 200);
      }

      // 3. User doesn't exist - create new user
      console.log(`Creating new user with UID: ${uid}`);
      
      const newUserData = {
        id: uid, // Use Firebase UID as user ID
        email: body.email,
        displayName: body.displayName,
        photoURL: body.photoURL,
        credits: 0, // Will be updated after registration processing
      };

      const newUser = await userService.createUserWithId(uid, newUserData);

      // 4. Process new user registration asynchronously
      c.executionCtx.waitUntil(
        this.processNewUserRegistration(uid, newUser, container)
      );

      // 5. Return new user data immediately
      return c.json({
        user: newUser,
        is_new_user: true,
        message: "New user created successfully"
      }, 201);

    } catch (error: any) {
      console.error("Error in check-or-create user:", error);
      return c.json({ error: "Internal server error" }, 500);
    }
  }

  /**
   * Process new user registration logic asynchronously
   */
  private async processNewUserRegistration(
    userId: string,
    userData: any,
    container: any
  ): Promise<void> {
    try {
      console.log(`Processing registration for new user: ${userId}`);

      const userService = container.resolve<UserService>(IUserService);

      // 1. Grant signup credits
      const signupCredits = 100; // Configure as needed
      await userService.addCredits(userId, signupCredits, "Signup bonus");
      console.log(`Granted ${signupCredits} signup credits to user ${userId}`);

      // 2. Send welcome email (if email service is available)
      try {
        // TODO: Implement email service
        // const emailService = container.resolve<EmailService>(IEmailService);
        // await emailService.sendWelcomeEmail(userData.email, userData.displayName);
        console.log(`Welcome email would be sent to: ${userData.email}`);
      } catch (emailError) {
        console.error(`Failed to send welcome email to ${userData.email}:`, emailError);
        // Don't fail the whole process if email fails
      }

      // 3. Log registration event
      console.log(`User registration completed successfully for: ${userId}`);

      // 4. Additional registration logic can be added here
      // - Analytics tracking
      // - Slack/Discord notifications
      // - Third-party integrations

    } catch (error) {
      console.error(`Failed to process registration for user ${userId}:`, error);
      // Consider implementing retry logic or dead letter queue for failed registrations
    }
  }
}
