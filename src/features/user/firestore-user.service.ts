import type {
  User,
  UpdateUserSubscriptionData,
  UpdateUserCreditsData,
  Subscription,
} from "./user.schema";
import { UserService } from "./user.interface";
import { inject, singleton } from "tsyringe";
import {
  IDbService,
  type DbService,
} from "../../infrastructure/db/db-service.interface";
import type { CollectionReference } from "firebase-rest-firestore";

const COLLECTION_NAME = "users";
// Firestore UserService
@singleton()
export class FireStoreUserService implements UserService {
  private usersCollection: CollectionReference;

  constructor(@inject(IDbService) dbService: DbService) {
    this.usersCollection = dbService
      .getFirestoreInstance()
      .collection(COLLECTION_NAME);
  }

  // 创建用户
  async createUser(userData: Omit<User, "id" | "createdAt">): Promise<User> {
    const docData = {
      ...userData,
      createdAt: new Date(),
    };

    const docRef = await this.usersCollection.add(docData);
    return {
      id: docRef.id,
      ...docData,
    };
  }

  // 创建用户（指定ID）
  async createUserWithId(
    id: string,
    userData: Omit<User, "id" | "createdAt">
  ): Promise<User> {
    const docData = {
      ...userData,
      createdAt: new Date(),
    };

    await this.usersCollection.doc(id).set(docData);
    return {
      id,
      ...docData,
    };
  }

  // 获取所有用户
  async getAllUsers(): Promise<User[]> {
    const snapshot = await this.usersCollection.get();
    return snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as User[];
  }

  // 通过ID获取用户
  async getUserById(id: string): Promise<User | null> {
    try {
      const doc = await this.usersCollection.doc(id).get();
      if (!doc.exists) {
        return null;
      }
      return {
        id: doc.id,
        ...doc.data(),
      } as User;
    } catch (error: any) {
      console.error(`Error getting user with ID ${id}:`, error);
      throw new Error(`Failed to retrieve user: ${error.message}`);
    }
  }

  // 更新用户
  async updateUser(
    id: string,
    userData: Partial<Omit<User, "id">>
  ): Promise<boolean> {
    const userRef = this.usersCollection.doc(id);
    const doc = await userRef.get();

    if (!doc.exists) {
      return false;
    }

    await userRef.update(userData);
    return true;
  }

  // 删除用户
  async deleteUser(id: string): Promise<boolean> {
    const userRef = this.usersCollection.doc(id);
    const doc = await userRef.get();

    if (!doc.exists) {
      return false;
    }

    await userRef.delete();
    return true;
  }

  // 更新用户订阅信息
  async updateUserSubscription(
    userId: string,
    subscriptionData: UpdateUserSubscriptionData
  ): Promise<boolean> {
    try {
      const userRef = this.usersCollection.doc(userId);
      const doc = await userRef.get();

      if (!doc.exists) {
        console.error(
          `User with ID ${userId} not found for subscription update`
        );
        return false;
      }

      await userRef.update({
        subscription: subscriptionData,
        updatedAt: new Date(),
      });

      console.log(`Updated subscription for user ${userId}:`, subscriptionData);
      return true;
    } catch (error: any) {
      console.error(`Error updating subscription for user ${userId}:`, error);
      throw new Error(`Failed to update user subscription: ${error.message}`);
    }
  }

  // 更新用户积分
  async updateUserCredits(
    userId: string,
    creditsData: UpdateUserCreditsData
  ): Promise<boolean> {
    try {
      const userRef = this.usersCollection.doc(userId);
      const doc = await userRef.get();

      if (!doc.exists) {
        console.error(`User with ID ${userId} not found for credits update`);
        return false;
      }

      await userRef.update({
        credits: creditsData.credits,
        updatedAt: new Date(),
      });

      console.log(`Updated credits for user ${userId}:`, creditsData.credits);
      return true;
    } catch (error: any) {
      console.error(`Error updating credits for user ${userId}:`, error);
      throw new Error(`Failed to update user credits: ${error.message}`);
    }
  }

  // 获取用户订阅状态
  async getUserSubscriptionStatus(
    userId: string
  ): Promise<Subscription | null> {
    try {
      const doc = await this.usersCollection.doc(userId).get();
      if (!doc.exists) {
        return null;
      }

      const userData = doc.data() as User;
      return userData.subscription || null;
    } catch (error: any) {
      console.error(
        `Error getting subscription status for user ${userId}:`,
        error
      );
      throw new Error(
        `Failed to retrieve user subscription status: ${error.message}`
      );
    }
  }
}
